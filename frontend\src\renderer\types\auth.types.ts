// Auth Types
export interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  role: string;
}

export interface AvailableShiftUser {
  id: string;
  firstName: string;
  lastName: string;
  username: string;
  role: string;
  shiftDetails: string;
}

export interface LoginRequest {
  userId: string;
  pin: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  user?: User;
  shiftStarted?: boolean;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

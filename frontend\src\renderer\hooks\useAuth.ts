import { useState } from 'react';
import { useAuthStore } from '../store/authStore';
import { useShiftStore } from '../store/shiftStore';
import { apiService } from '../services/api.service';
import { LoginRequest } from '../types/auth.types';

export const useAuth = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { setAuth, logout } = useAuthStore();
  const { startShift } = useShiftStore();

  const pinLogin = async (request: LoginRequest) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiService.pinLogin(request);
      
      if (response.success && response.user && response.token) {
        setAuth(response.user, response.token);
        
        if (response.shiftStarted) {
          startShift(response.user.id);
        }
        
        return { success: true };
      } else {
        setError(response.error || '<PERSON><PERSON><PERSON> ba<PERSON>ı<PERSON>');
        return { success: false, error: response.error };
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Bağlantı hatası';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => setError(null);

  return {
    pinLogin,
    loading,
    error,
    clearError,
    logout
  };
};

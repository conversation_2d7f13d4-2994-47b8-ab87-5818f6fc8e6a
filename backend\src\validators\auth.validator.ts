import { z } from 'zod';

export const pinLoginSchema = z.object({
  body: z.object({
    userId: z.string().min(1, 'User ID is required'),
    pin: z.string().length(6, 'PIN must be exactly 6 digits').regex(/^\d{6}$/, 'PIN must contain only digits')
  })
});

export const availableShiftsSchema = z.object({
  query: z.object({
    branchId: z.string().optional()
  })
});

export type PinLoginRequest = z.infer<typeof pinLoginSchema>['body'];
export type AvailableShiftsQuery = z.infer<typeof availableShiftsSchema>['query'];

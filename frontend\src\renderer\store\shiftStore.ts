import { create } from 'zustand';

interface ShiftState {
  currentShift: {
    id?: string;
    startedAt?: Date;
    isActive: boolean;
  } | null;
  startShift: (shiftId: string) => void;
  endShift: () => void;
  clearShift: () => void;
}

export const useShiftStore = create<ShiftState>((set) => ({
  currentShift: null,
  
  startShift: (shiftId: string) => {
    set({
      currentShift: {
        id: shiftId,
        startedAt: new Date(),
        isActive: true,
      },
    });
  },
  
  endShift: () => {
    set((state) => ({
      currentShift: state.currentShift
        ? { ...state.currentShift, isActive: false }
        : null,
    }));
  },
  
  clearShift: () => {
    set({ currentShift: null });
  },
}));

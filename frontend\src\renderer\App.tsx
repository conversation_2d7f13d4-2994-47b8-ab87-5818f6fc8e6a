import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAuthStore } from './store/authStore';
import { PinLogin } from './pages/PinLogin';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { isAuthenticated } = useAuthStore();

  return (
    <QueryClientProvider client={queryClient}>
      {isAuthenticated ? (
        <div className="min-h-screen bg-gray-100 p-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">
              Dashboard - Yakında Gelecek
            </h1>
            <div className="bg-white rounded-lg shadow p-6">
              <p className="text-gray-600">
                <PERSON><PERSON><PERSON> başarılı! Dashboard sayfası geliştirme aşamasında...
              </p>
            </div>
          </div>
        </div>
      ) : (
        <PinLogin />
      )}
    </QueryClientProvider>
  );
}

export default App

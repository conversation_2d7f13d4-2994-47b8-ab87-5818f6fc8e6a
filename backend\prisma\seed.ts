import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create a test company
  const company = await prisma.company.upsert({
    where: { taxNumber: '1234567890' },
    update: {},
    create: {
      name: 'Atropos Restaurant',
      taxNumber: '1234567890',
      taxOffice: 'Kadıköy',
      address: 'Test Address',
      phone: '+90 ************',
      email: '<EMAIL>'
    }
  });

  // Create a test branch
  const branch = await prisma.branch.upsert({
    where: { 
      companyId_code: {
        companyId: company.id,
        code: 'MAIN'
      }
    },
    update: {},
    create: {
      companyId: company.id,
      code: 'MA<PERSON>',
      name: '<PERSON>',
      address: 'Ana Şube Adresi',
      phone: '+90 ************',
      isMainBranch: true
    }
  });

  // Hash PIN for test users
  const hashedPin = await bcrypt.hash('123456', 10);

  // Create test users
  const users = [
    {
      username: 'elisaklein',
      firstName: '<PERSON><PERSON>',
      lastName: '<PERSON>',
      role: 'CASHIER',
      pin: hashedPin
    },
    {
      username: 'ahmadkusuma',
      firstName: 'Ahmad',
      lastName: 'Kusuma',
      role: 'WAITER',
      pin: hashedPin
    },
    {
      username: 'intanfauziah',
      firstName: 'Intan',
      lastName: 'Fauziah',
      role: 'WAITER',
      pin: hashedPin
    }
  ];

  for (const userData of users) {
    await prisma.user.upsert({
      where: { username: userData.username },
      update: {},
      create: {
        ...userData,
        companyId: company.id,
        branchId: branch.id,
        password: await bcrypt.hash('password123', 10), // Default password
        active: true
      }
    });
  }

  console.log('✅ Seeding completed!');
  console.log('📋 Test users created with PIN: 123456');
  console.log('   - elisaklein (CASHIER)');
  console.log('   - ahmadkusuma (WAITER)');
  console.log('   - intanfauziah (WAITER)');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

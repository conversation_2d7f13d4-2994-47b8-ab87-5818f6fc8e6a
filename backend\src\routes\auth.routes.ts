import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller.js';
import { validate } from '../middlewares/validation.middleware.js';
import { pinLoginSchema, availableShiftsSchema } from '../validators/auth.validator.js';
import { PrismaClient } from '@prisma/client';

export const createAuthRoutes = (prisma: PrismaClient): Router => {
  const router = Router();
  const authController = new AuthController(prisma);

  // GET /api/auth/available-shifts
  router.get(
    '/available-shifts',
    validate(availableShiftsSchema),
    authController.getAvailableShifts
  );

  // POST /api/auth/pin-login
  router.post(
    '/pin-login',
    validate(pinLoginSchema),
    authController.pinLogin
  );

  return router;
};

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { AvailableShiftUser, LoginRequest, LoginResponse } from '../types/auth.types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          // Redirect to login if needed
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async getAvailableShifts(branchId?: string): Promise<AvailableShiftUser[]> {
    const params = branchId ? { branchId } : {};
    const response = await this.api.get('/users/available-shifts', { params });
    return response.data;
  }

  async pinLogin(request: LoginRequest): Promise<LoginResponse> {
    const response = await this.api.post('/auth/pin-login', request);
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.api.get('/health');
    return response.data;
  }
}

export const apiService = new ApiService();

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { createError } from '../middlewares/error.middleware.js';
import { LoginRequest, LoginResponse, AvailableShiftUser } from '../types/auth.types.js';

export class AuthService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async getAvailableShifts(branchId?: string): Promise<AvailableShiftUser[]> {
    const whereClause: any = {
      active: true,
      pin: { not: null } // PIN'i olan kullan<PERSON>ılar
    };

    if (branchId) {
      whereClause.OR = [
        { branchId: branchId },
        { branchId: null } // Tüm şubelere erişimi olan kullan<PERSON>ılar
      ];
    }

    const users = await this.prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        username: true,
        role: true
      }
    });

    return users.map(user => ({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      username: user.username,
      role: user.role,
      shiftDetails: this.generateShiftDetails(user.role)
    }));
  }

  async pinLogin(request: LoginRequest): Promise<LoginResponse> {
    // PIN validation
    if (!request.pin || request.pin.length !== 6) {
      return {
        success: false,
        error: 'PIN 6 haneli olmalıdır'
      };
    }

    // User lookup
    const user = await this.prisma.user.findFirst({
      where: {
        id: request.userId,
        active: true,
        pin: { not: null }
      }
    });

    if (!user) {
      return {
        success: false,
        error: 'Kullanıcı bulunamadı veya aktif değil'
      };
    }

    // PIN verification
    const isPinValid = await bcrypt.compare(request.pin, user.pin!);
    if (!isPinValid) {
      return {
        success: false,
        error: 'Geçersiz PIN'
      };
    }

    // Update last login
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Create session
    const token = this.generateToken(user);
    await this.createSession(user.id, user.branchId || '', token);

    return {
      success: true,
      token,
      user: {
        id: user.id,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      },
      shiftStarted: true
    };
  }

  private generateToken(user: any): string {
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw createError('JWT secret not configured', 500);
    }

    return jwt.sign(
      {
        id: user.id,
        username: user.username,
        role: user.role,
        branchId: user.branchId
      },
      jwtSecret,
      { expiresIn: '24h' }
    );
  }

  private async createSession(userId: string, branchId: string, token: string): Promise<void> {
    await this.prisma.session.create({
      data: {
        userId,
        branchId,
        token,
        deviceInfo: 'POS Terminal'
      }
    });
  }

  private generateShiftDetails(role: string): string {
    // Bu gerçek bir vardiya sistemi olsaydı, veritabanından gelirdi
    // Şimdilik role'e göre örnek vardiya saatleri döndürüyoruz
    switch (role) {
      case 'CASHIER':
        return '04:00 PM - 12:00 PM';
      case 'WAITER':
        return '08:00 AM - 04:00 PM';
      case 'KITCHEN':
        return '06:00 AM - 02:00 PM';
      case 'BRANCH_MANAGER':
        return '09:00 AM - 06:00 PM';
      default:
        return '09:00 AM - 05:00 PM';
    }
  }
}

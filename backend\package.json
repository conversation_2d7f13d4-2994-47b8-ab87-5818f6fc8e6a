{"name": "pos-backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"express": "^4.21.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.4.7", "@prisma/client": "^6.7.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^22.10.2", "typescript": "^5.7.2", "tsx": "^4.19.7", "prisma": "^6.7.0"}}
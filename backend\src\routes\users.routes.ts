import { Router } from 'express';
import { AuthController } from '../controllers/auth.controller.js';
import { validate } from '../middlewares/validation.middleware.js';
import { availableShiftsSchema } from '../validators/auth.validator.js';
import { PrismaClient } from '@prisma/client';

export const createUsersRoutes = (prisma: PrismaClient): Router => {
  const router = Router();
  const authController = new AuthController(prisma);

  // GET /api/users/available-shifts
  router.get(
    '/available-shifts',
    validate(availableShiftsSchema),
    authController.getAvailableShifts
  );

  return router;
};

import { Request, Response } from 'express';
import { AuthService } from '../services/auth.service.js';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../middlewares/error.middleware.js';

export class AuthController {
  private authService: AuthService;

  constructor(prisma: PrismaClient) {
    this.authService = new AuthService(prisma);
  }

  getAvailableShifts = asyncHandler(async (req: Request, res: Response) => {
    const branchId = req.query.branchId as string;
    
    const users = await this.authService.getAvailableShifts(branchId);
    
    res.json(users);
  });

  pinLogin = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.authService.pinLogin(req.body);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(401).json(result);
    }
  });
}
